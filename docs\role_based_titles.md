# Role-Based Titles Feature

This document explains how to use the role-based titles feature in the SiMenang KRPG app.

## Overview

The role-based titles feature automatically displays the user's role (Coach, Athlete, Leader, Guest) in page titles throughout the app. This helps users clearly understand which role they are accessing the app as.

## Examples

- **Coach** accessing the Home page will see: "Coach Home"
- **Athlete** accessing the Training page will see: "Athlete Training"
- **Leader** accessing the Athletes page will see: "Leader Athletes"

## Implementation Methods

### Method 1: Using RoleTitleHelper (Recommended)

The easiest way to implement role-based titles is using the `RoleTitleHelper` utility class:

```dart
import '../../../utils/role_title_helper.dart';

// In your screen's build method:
appBar: AppBar(
  title: RoleTitleHelper.createRoleBasedTitle(context, 'Home'),
  backgroundColor: KRPGTheme.primaryColor,
  foregroundColor: Colors.white,
),
```

### Method 2: Using KRPGAppBar with showRoleInTitle

You can use the enhanced KRPGAppBar component:

```dart
appBar: KRPGAppBar.primary(
  title: 'Home',
  showRoleInTitle: true,
),
```

### Method 3: Using KRPGScaffold with showRoleInTitle

For screens using KRPGScaffold:

```dart
return KRPGScaffold.primary(
  title: 'Home',
  showRoleInTitle: true,
  body: YourBodyWidget(),
);
```

### Method 4: Manual Implementation

For custom implementations:

```dart
appBar: AppBar(
  title: Consumer<AuthController>(
    builder: (context, authController, child) {
      final userRole = authController.currentUser?.role.displayName ?? '';
      final roleBasedTitle = userRole.isNotEmpty ? '$userRole Home' : 'Home';
      return Text(roleBasedTitle);
    },
  ),
),
```

## RoleTitleHelper Utility Methods

The `RoleTitleHelper` class provides several useful methods:

### createRoleBasedTitle()
Creates a widget with role-based title that automatically updates.

### getRoleBasedTitle()
Returns a string with the role-based title without creating a widget.

### getCurrentUserRole()
Gets the current user's role display name.

### hasRole()
Checks if the current user has a specific role.

### getRoleColor()
Gets role-specific colors for UI elements.

### getRoleIcon()
Gets role-specific icons.

## User Roles

The app supports the following user roles:

- **Leader**: Administrative role with full access
- **Coach**: Training and athlete management role
- **Athlete**: Participant role with limited access
- **Guest**: Visitor role with minimal access

## Current Implementation Status

The following screens have been updated with role-based titles:

- ✅ Home Screen
- ✅ Training Screen
- ✅ Competition Screen
- ✅ Classroom Screen
- ✅ Athletes Screen
- ✅ Profile Screen

## Benefits

1. **Clear Role Identification**: Users always know which role they're accessing the app as
2. **Better User Experience**: Reduces confusion when users have multiple roles
3. **Consistent UI**: Standardized approach across all screens
4. **Easy Implementation**: Simple to add to new screens
5. **Automatic Updates**: Titles update automatically when user role changes

## Best Practices

1. Use `RoleTitleHelper.createRoleBasedTitle()` for new implementations
2. Keep base titles short and descriptive
3. Test with different user roles to ensure proper display
4. Consider role-specific colors and icons for enhanced UX

## Future Enhancements

Potential improvements for this feature:

- Role-specific color themes for app bars
- Role-based navigation restrictions
- Role-specific welcome messages
- Dynamic role switching interface
