import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../controllers/training_controller.dart';
import '../../../controllers/auth_controller.dart';
import '../../../components/cards/krpg_card.dart';
import '../../../components/ui/krpg_badge.dart';
import '../../../design_system/krpg_theme.dart';
import '../../../design_system/krpg_text_styles.dart';
import '../../../models/user_model.dart';

class AttendanceHistoryScreen extends StatefulWidget {
  const AttendanceHistoryScreen({super.key});

  @override
  State<AttendanceHistoryScreen> createState() => _AttendanceHistoryScreenState();
}

class _AttendanceHistoryScreenState extends State<AttendanceHistoryScreen> {
  List<Map<String, dynamic>> _attendanceHistory = [];
  bool _isLoading = true;
  String _selectedFilter = 'all';

  @override
  void initState() {
    super.initState();
    _loadAttendanceHistory();
  }

  Future<void> _loadAttendanceHistory() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final controller = context.read<TrainingController>();
      final history = await controller.getAthleteAttendanceHistory();
      
      setState(() {
        _attendanceHistory = history ?? _generateDummyAttendanceHistory();
      });
    } catch (e) {
      // Fallback to dummy data for UEQ-S testing
      setState(() {
        _attendanceHistory = _generateDummyAttendanceHistory();
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<Map<String, dynamic>> _generateDummyAttendanceHistory() {
    return [
      {
        'id': 'att_001',
        'training_name': 'Basic Karate Techniques',
        'date': '2025-01-30',
        'time': '19:00 - 21:00',
        'status': 'present',
        'check_in_time': '18:55',
        'check_out_time': '21:05',
        'location': 'Dojo Utama - Lantai 1',
        'coach': 'Sensei Budi Santoso',
        'notes': 'Great participation, good technique improvement',
        'performance_score': 8.5,
      },
      {
        'id': 'att_002',
        'training_name': 'Kumite Competition Prep',
        'date': '2025-01-28',
        'time': '17:30 - 19:30',
        'status': 'present',
        'check_in_time': '17:25',
        'check_out_time': '19:35',
        'location': 'Dojo Utama - Lantai 2',
        'coach': 'Sensei Ana Putri',
        'notes': 'Excellent sparring performance, ready for competition',
        'performance_score': 9.0,
      },
      {
        'id': 'att_003',
        'training_name': 'Kata Championship Training',
        'date': '2025-01-26',
        'time': '20:00 - 21:30',
        'status': 'late',
        'check_in_time': '20:15',
        'check_out_time': '21:30',
        'location': 'Dojo Utama - Lantai 1',
        'coach': 'Sensei Citra Wulan',
        'notes': 'Late arrival but good recovery, kata form improved',
        'performance_score': 7.8,
      },
      {
        'id': 'att_004',
        'training_name': 'Physical Conditioning',
        'date': '2025-01-24',
        'time': '18:00 - 19:00',
        'status': 'present',
        'check_in_time': '17:58',
        'check_out_time': '19:02',
        'location': 'Gym Area',
        'coach': 'Pelatih Fitnes Rio',
        'notes': 'Good endurance, need to work on flexibility',
        'performance_score': 8.2,
      },
      {
        'id': 'att_005',
        'training_name': 'Advanced Techniques',
        'date': '2025-01-22',
        'time': '19:30 - 21:00',
        'status': 'absent',
        'check_in_time': null,
        'check_out_time': null,
        'location': 'Dojo Utama - Lantai 1',
        'coach': 'Sensei Budi Santoso',
        'notes': 'Absent due to illness (excused)',
        'performance_score': null,
      },
      {
        'id': 'att_006',
        'training_name': 'Team Sparring Practice',
        'date': '2025-01-20',
        'time': '16:00 - 18:00',
        'status': 'present',
        'check_in_time': '15:55',
        'check_out_time': '18:10',
        'location': 'Dojo Utama - Lantai 2',
        'coach': 'Sensei Ana Putri',
        'notes': 'Excellent teamwork, good leadership skills shown',
        'performance_score': 8.8,
      },
    ];
  }

  List<Map<String, dynamic>> get _filteredHistory {
    if (_selectedFilter == 'all') {
      return _attendanceHistory;
    }
    return _attendanceHistory.where((record) => record['status'] == _selectedFilter).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Attendance History'),
        backgroundColor: KRPGTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _showAttendanceStats,
            tooltip: 'View Statistics',
          ),
        ],
      ),
      body: Consumer<AuthController>(
        builder: (context, authController, child) {
          // Only allow athletes to view their attendance history
          if (authController.currentUser?.role != UserRole.athlete) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.person_off,
                    size: 64,
                    color: KRPGTheme.neutralMedium,
                  ),
                  const SizedBox(height: KRPGTheme.spacingMd),
                  Text(
                    'Athlete Only Feature',
                    style: KRPGTextStyles.heading5,
                  ),
                  const SizedBox(height: KRPGTheme.spacingSm),
                  Text(
                    'This feature is only available for athletes',
                    style: KRPGTextStyles.bodyMedium.copyWith(
                      color: KRPGTheme.textMedium,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // Filter Section
              Container(
                padding: const EdgeInsets.all(KRPGTheme.spacingMd),
                child: _buildFilterSection(),
              ),
              
              // Content
              Expanded(
                child: _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : RefreshIndicator(
                        onRefresh: _loadAttendanceHistory,
                        child: _filteredHistory.isEmpty
                            ? _buildEmptyState()
                            : ListView.builder(
                                padding: const EdgeInsets.symmetric(horizontal: KRPGTheme.spacingMd),
                                itemCount: _filteredHistory.length,
                                itemBuilder: (context, index) {
                                  final record = _filteredHistory[index];
                                  return _buildAttendanceCard(record);
                                },
                              ),
                      ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildFilterSection() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          _buildFilterChip('All', 'all'),
          const SizedBox(width: KRPGTheme.spacingSm),
          _buildFilterChip('Present', 'present'),
          const SizedBox(width: KRPGTheme.spacingSm),
          _buildFilterChip('Late', 'late'),
          const SizedBox(width: KRPGTheme.spacingSm),
          _buildFilterChip('Absent', 'absent'),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String value) {
    final isSelected = _selectedFilter == value;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedFilter = value;
        });
      },
      backgroundColor: isSelected ? KRPGTheme.primaryColor.withOpacity(0.1) : null,
      selectedColor: KRPGTheme.primaryColor.withOpacity(0.2),
      checkmarkColor: KRPGTheme.primaryColor,
    );
  }

  Widget _buildAttendanceCard(Map<String, dynamic> record) {
    return Padding(
      padding: const EdgeInsets.only(bottom: KRPGTheme.spacingMd),
      child: KRPGCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Text(
                    record['training_name'],
                    style: KRPGTextStyles.heading6,
                  ),
                ),
                KRPGBadge(
                  text: record['status'].toString().toUpperCase(),
                  backgroundColor: _getAttendanceStatusColor(record['status']).withOpacity(0.1),
                  textColor: _getAttendanceStatusColor(record['status']),
                ),
              ],
            ),
            const SizedBox(height: KRPGTheme.spacingSm),
            
            // Basic Info
            Row(
              children: [
                Icon(Icons.calendar_today, size: 16, color: KRPGTheme.textMedium),
                const SizedBox(width: KRPGTheme.spacingXs),
                Text(
                  record['date'],
                  style: KRPGTextStyles.bodySmall.copyWith(color: KRPGTheme.textMedium),
                ),
                const SizedBox(width: KRPGTheme.spacingMd),
                Icon(Icons.access_time, size: 16, color: KRPGTheme.textMedium),
                const SizedBox(width: KRPGTheme.spacingXs),
                Text(
                  record['time'],
                  style: KRPGTextStyles.bodySmall.copyWith(color: KRPGTheme.textMedium),
                ),
              ],
            ),
            const SizedBox(height: KRPGTheme.spacingXs),
            
            Row(
              children: [
                Icon(Icons.person, size: 16, color: KRPGTheme.textMedium),
                const SizedBox(width: KRPGTheme.spacingXs),
                Text(
                  record['coach'],
                  style: KRPGTextStyles.bodySmall.copyWith(color: KRPGTheme.textMedium),
                ),
                const SizedBox(width: KRPGTheme.spacingMd),
                Icon(Icons.location_on, size: 16, color: KRPGTheme.textMedium),
                const SizedBox(width: KRPGTheme.spacingXs),
                Expanded(
                  child: Text(
                    record['location'],
                    style: KRPGTextStyles.bodySmall.copyWith(color: KRPGTheme.textMedium),
                  ),
                ),
              ],
            ),
            
            // Attendance Details
            if (record['status'] != 'absent') ...[
              const SizedBox(height: KRPGTheme.spacingSm),
              Container(
                padding: const EdgeInsets.all(KRPGTheme.spacingSm),
                decoration: BoxDecoration(
                  color: KRPGTheme.backgroundAccent,
                  borderRadius: BorderRadius.circular(KRPGTheme.radiusSm),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          'Check-in: ${record['check_in_time']}',
                          style: KRPGTextStyles.bodySmall,
                        ),
                        const SizedBox(width: KRPGTheme.spacingMd),
                        Text(
                          'Check-out: ${record['check_out_time']}',
                          style: KRPGTextStyles.bodySmall,
                        ),
                      ],
                    ),
                    if (record['performance_score'] != null) ...[
                      const SizedBox(height: KRPGTheme.spacingXs),
                      Row(
                        children: [
                          Text(
                            'Performance Score: ',
                            style: KRPGTextStyles.bodySmall,
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: KRPGTheme.spacingXs,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: _getPerformanceColor(record['performance_score']).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(KRPGTheme.radiusXs),
                            ),
                            child: Text(
                              record['performance_score'].toString(),
                              style: KRPGTextStyles.bodySmall.copyWith(
                                color: _getPerformanceColor(record['performance_score']),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                    if (record['notes'] != null) ...[
                      const SizedBox(height: KRPGTheme.spacingXs),
                      Text(
                        'Notes: ${record['notes']}',
                        style: KRPGTextStyles.bodySmall.copyWith(
                          color: KRPGTheme.textMedium,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ] else ...[
              const SizedBox(height: KRPGTheme.spacingSm),
              if (record['notes'] != null)
                Container(
                  padding: const EdgeInsets.all(KRPGTheme.spacingSm),
                  decoration: BoxDecoration(
                    color: KRPGTheme.dangerColorLight,
                    borderRadius: BorderRadius.circular(KRPGTheme.radiusSm),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        size: 16,
                        color: KRPGTheme.dangerColor,
                      ),
                      const SizedBox(width: KRPGTheme.spacingXs),
                      Expanded(
                        child: Text(
                          record['notes'],
                          style: KRPGTextStyles.bodySmall.copyWith(
                            color: KRPGTheme.dangerColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.event_busy,
            size: 64,
            color: KRPGTheme.neutralMedium,
          ),
          const SizedBox(height: KRPGTheme.spacingMd),
          Text(
            'No Attendance Records',
            style: KRPGTextStyles.heading5,
          ),
          const SizedBox(height: KRPGTheme.spacingSm),
          Text(
            'Your training attendance will appear here',
            style: KRPGTextStyles.bodyMedium.copyWith(
              color: KRPGTheme.textMedium,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Color _getAttendanceStatusColor(String status) {
    switch (status) {
      case 'present':
        return KRPGTheme.successColor;
      case 'late':
        return KRPGTheme.warningColor;
      case 'absent':
      default:
        return KRPGTheme.dangerColor;
    }
  }

  Color _getPerformanceColor(double score) {
    if (score >= 8.5) {
      return KRPGTheme.successColor;
    } else if (score >= 7.0) {
      return KRPGTheme.warningColor;
    } else {
      return KRPGTheme.dangerColor;
    }
  }

  void _showAttendanceStats() {
    final presentCount = _attendanceHistory.where((r) => r['status'] == 'present').length;
    final lateCount = _attendanceHistory.where((r) => r['status'] == 'late').length;
    final absentCount = _attendanceHistory.where((r) => r['status'] == 'absent').length;
    final totalCount = _attendanceHistory.length;
    final attendanceRate = totalCount > 0 ? ((presentCount + lateCount) / totalCount * 100).toStringAsFixed(1) : '0.0';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Attendance Statistics'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStatRow('Total Sessions', totalCount.toString()),
            _buildStatRow('Present', presentCount.toString()),
            _buildStatRow('Late', lateCount.toString()),
            _buildStatRow('Absent', absentCount.toString()),
            const Divider(),
            _buildStatRow('Attendance Rate', '$attendanceRate%'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.w600),
          ),
        ],
      ),
    );
  }
}
