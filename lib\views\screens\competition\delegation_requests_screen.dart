import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../controllers/competition_controller.dart';
import '../../../controllers/auth_controller.dart';
import '../../../components/cards/krpg_card.dart';
import '../../../components/buttons/krpg_button.dart';
import '../../../components/ui/krpg_badge.dart';
import '../../../design_system/krpg_theme.dart';
import '../../../design_system/krpg_text_styles.dart';
import '../../../models/user_model.dart';

class DelegationRequestsScreen extends StatefulWidget {
  const DelegationRequestsScreen({super.key});

  @override
  State<DelegationRequestsScreen> createState() => _DelegationRequestsScreenState();
}

class _DelegationRequestsScreenState extends State<DelegationRequestsScreen> {
  List<Map<String, dynamic>> _delegationRequests = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDelegationRequests();
  }

  Future<void> _loadDelegationRequests() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final controller = context.read<CompetitionController>();
      final requests = await controller.getDelegationRequests();
      
      setState(() {
        _delegationRequests = requests ?? _generateDummyRequests();
      });
    } catch (e) {
      // Fallback to dummy data for UEQ-S testing
      setState(() {
        _delegationRequests = _generateDummyRequests();
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<Map<String, dynamic>> _generateDummyRequests() {
    return [
      {
        'id': 'req_001',
        'athlete_name': 'Ahmad Ridwan',
        'athlete_id': 'ath_001',
        'competition_name': 'Kejuaraan Nasional Karate 2025',
        'competition_id': 'comp_001',
        'requested_by': 'Pelatih Budi Santoso',
        'request_date': '2025-01-25',
        'status': 'pending',
        'category': 'Kumite Senior Putra -75kg',
        'reason': 'Atlet memiliki prestasi yang baik dan siap untuk kompetisi nasional',
      },
      {
        'id': 'req_002',
        'athlete_name': 'Sari Dewi',
        'athlete_id': 'ath_002',
        'competition_name': 'Piala Walikota Karate Championship',
        'competition_id': 'comp_002',
        'requested_by': 'Pelatih Ana Putri',
        'request_date': '2025-01-24',
        'status': 'pending',
        'category': 'Kata Senior Putri',
        'reason': 'Atlet sudah menguasai kata dengan baik dan membutuhkan pengalaman kompetisi',
      },
      {
        'id': 'req_003',
        'athlete_name': 'Budi Prakoso',
        'athlete_id': 'ath_003',
        'competition_name': 'Kejuaraan Daerah Karate Open',
        'competition_id': 'comp_003',
        'requested_by': 'Pelatih Citra Wulan',
        'request_date': '2025-01-23',
        'status': 'approved',
        'category': 'Kumite Junior Putra -65kg',
        'reason': 'Atlet sudah siap dan memiliki mental yang kuat',
      },
    ];
  }

  Future<void> _handleApproval(String requestId, bool approved) async {
    try {
      final controller = context.read<CompetitionController>();
      
      if (approved) {
        await controller.approveDelegationRequest(requestId);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Delegation request approved successfully!'),
            backgroundColor: KRPGTheme.successColor,
          ),
        );
      } else {
        await controller.rejectDelegationRequest(requestId);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Delegation request rejected.'),
            backgroundColor: KRPGTheme.dangerColor,
          ),
        );
      }
      
      // Reload the requests
      _loadDelegationRequests();
    } catch (e) {
      // For UEQ-S testing, simulate the action
      setState(() {
        final index = _delegationRequests.indexWhere((req) => req['id'] == requestId);
        if (index != -1) {
          _delegationRequests[index]['status'] = approved ? 'approved' : 'rejected';
        }
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(approved 
            ? 'Delegation request approved successfully!' 
            : 'Delegation request rejected.'),
          backgroundColor: approved ? KRPGTheme.successColor : KRPGTheme.dangerColor,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Delegation Requests'),
        backgroundColor: KRPGTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Consumer<AuthController>(
        builder: (context, authController, child) {
          // Only allow ketua/admin to access this screen
          if (authController.currentUser?.role != UserRole.coach && 
              authController.currentUser?.username != '<EMAIL>') {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.lock,
                    size: 64,
                    color: KRPGTheme.neutralMedium,
                  ),
                  const SizedBox(height: KRPGTheme.spacingMd),
                  Text(
                    'Access Restricted',
                    style: KRPGTextStyles.heading5,
                  ),
                  const SizedBox(height: KRPGTheme.spacingSm),
                  Text(
                    'Only ketua/admin can approve delegation requests',
                    style: KRPGTextStyles.bodyMedium.copyWith(
                      color: KRPGTheme.textMedium,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }

          if (_isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return RefreshIndicator(
            onRefresh: _loadDelegationRequests,
            child: ListView(
              padding: const EdgeInsets.all(KRPGTheme.spacingMd),
              children: [
                Text(
                  'Pending Requests',
                  style: KRPGTextStyles.heading5,
                ),
                const SizedBox(height: KRPGTheme.spacingMd),
                ..._delegationRequests
                    .where((req) => req['status'] == 'pending')
                    .map((request) => _buildRequestCard(request, true)),
                
                const SizedBox(height: KRPGTheme.spacingLg),
                Text(
                  'Processed Requests',
                  style: KRPGTextStyles.heading5,
                ),
                const SizedBox(height: KRPGTheme.spacingMd),
                ..._delegationRequests
                    .where((req) => req['status'] != 'pending')
                    .map((request) => _buildRequestCard(request, false)),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildRequestCard(Map<String, dynamic> request, bool isPending) {
    return Padding(
      padding: const EdgeInsets.only(bottom: KRPGTheme.spacingMd),
      child: KRPGCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    request['athlete_name'],
                    style: KRPGTextStyles.heading6,
                  ),
                ),
                KRPGBadge(
                  text: request['status'].toString().toUpperCase(),
                  backgroundColor: _getBadgeColor(request['status']).withOpacity(0.1),
                  textColor: _getBadgeColor(request['status']),
                ),
              ],
            ),
            const SizedBox(height: KRPGTheme.spacingSm),
            
            Text(
              'Competition: ${request['competition_name']}',
              style: KRPGTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: KRPGTheme.spacingXs),
            
            Text(
              'Category: ${request['category']}',
              style: KRPGTextStyles.bodySmall.copyWith(
                color: KRPGTheme.textMedium,
              ),
            ),
            const SizedBox(height: KRPGTheme.spacingXs),
            
            Text(
              'Requested by: ${request['requested_by']}',
              style: KRPGTextStyles.bodySmall.copyWith(
                color: KRPGTheme.textMedium,
              ),
            ),
            const SizedBox(height: KRPGTheme.spacingXs),
            
            Text(
              'Date: ${request['request_date']}',
              style: KRPGTextStyles.bodySmall.copyWith(
                color: KRPGTheme.textMedium,
              ),
            ),
            
            if (request['reason'] != null) ...[
              const SizedBox(height: KRPGTheme.spacingSm),
              Container(
                padding: const EdgeInsets.all(KRPGTheme.spacingSm),
                decoration: BoxDecoration(
                  color: KRPGTheme.backgroundAccent,
                  borderRadius: BorderRadius.circular(KRPGTheme.radiusSm),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Reason:',
                      style: KRPGTextStyles.bodySmall.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: KRPGTheme.spacingXs),
                    Text(
                      request['reason'],
                      style: KRPGTextStyles.bodySmall,
                    ),
                  ],
                ),
              ),
            ],
            
            if (isPending) ...[
              const SizedBox(height: KRPGTheme.spacingMd),
              Row(
                children: [
                  Expanded(
                    child: KRPGButton(
                      text: 'Reject',
                      onPressed: () => _showConfirmationDialog(
                        request['id'], 
                        false, 
                        request['athlete_name']
                      ),
                      type: KRPGButtonType.outlined,
                      borderColor: KRPGTheme.dangerColor,
                      textColor: KRPGTheme.dangerColor,
                    ),
                  ),
                  const SizedBox(width: KRPGTheme.spacingMd),
                  Expanded(
                    child: KRPGButton(
                      text: 'Approve',
                      onPressed: () => _showConfirmationDialog(
                        request['id'], 
                        true, 
                        request['athlete_name']
                      ),
                      type: KRPGButtonType.filled,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Color _getBadgeColor(String status) {
    switch (status) {
      case 'approved':
        return KRPGTheme.successColor;
      case 'rejected':
        return KRPGTheme.dangerColor;
      case 'pending':
      default:
        return KRPGTheme.warningColor;
    }
  }

  void _showConfirmationDialog(String requestId, bool approved, String athleteName) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(approved ? 'Approve Request' : 'Reject Request'),
        content: Text(
          approved 
            ? 'Are you sure you want to approve the delegation request for $athleteName?'
            : 'Are you sure you want to reject the delegation request for $athleteName?'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _handleApproval(requestId, approved);
            },
            child: Text(approved ? 'Approve' : 'Reject'),
          ),
        ],
      ),
    );
  }
}
