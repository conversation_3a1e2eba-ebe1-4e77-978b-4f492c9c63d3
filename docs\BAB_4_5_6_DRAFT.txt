### BAB IV
### IMPLEMENTASI DAN PENGUMPULAN DATA

Pada bab ini diuraikan secara rinci proses dan hasil dari implementasi metode gabungan *User-Centered Design* (UCD) dan *Scrum* dalam pengembangan aplikasi mobile untuk Manajemen Klub Renang Petrokimia Gresik. Pembahasan mencakup tahapan-tahapan implementasi mulai dari riset pengguna, perancangan, pengembangan per-sprint, hingga metode pengumpulan data yang digunakan selama siklus penelitian berlangsung.

#### 4.1 Implementasi Metode UCD dan Scrum
Implementasi metodologi dibagi menjadi tiga fase utama sesuai dengan alur yang telah dirancang pada Bab III, yaitu:
1.  **Fase User Research & Planning (Pra-Sprint):** Berfokus pada pemahaman konteks, pengguna, dan kebutuhan untuk membentuk dasar dari *Product Backlog*.
2.  **Fase Design Sprint (Sprint 0):** Berfokus pada perancangan dan validasi *High-Fidelity Prototype* sebelum pengembangan.
3.  **Fase Development Sprint (Sprint 1, 2, dan 3):** Siklus pengembangan iteratif untuk mengimplementasikan fitur-fitur dari *Sprint Backlog* menjadi produk fungsional.

---
#### 4.1.1 Fase User Research & Planning (Pra-Sprint)
Fase awal ini merupakan fondasi dari keseluruhan proyek, di mana aktivitas UCD paling intensif dilakukan untuk memastikan produk yang dikembangkan benar-benar menjawab permasalahan yang ada.

##### 4.1.1.1 Observasi dan Wawancara
Langkah pertama adalah melakukan observasi langsung terhadap proses bisnis di Klub Renang Petrokimia Gresik dan wawancara mendalam dengan para pemangku kepentingan.
*   **Pelaksanaan:** Wawancara dilakukan pada [Masukkan Tanggal] bersama Bapak Chandra (Pelatih) dan Bapak Satria (Sekretaris Klub).
*   **Tujuan:** Menggali permasalahan (*pain points*), alur kerja manual yang ada, dan harapan terhadap sistem digital yang akan dibangun.
*   **Hasil:** Ditemukan beberapa kendala utama, yaitu (1) Proses seleksi atlet untuk kompetisi tidak efisien karena data prestasi tidak terstruktur; (2) Pencatatan data latihan masih manual dan tidak *real-time*; (3) Atlet kesulitan memantau perkembangan performa pribadi. Dokumentasi lengkap hasil wawancara terlampir pada **Lampiran A**.

##### ******* Perancangan Product Backlog dan Kebutuhan Sistem
Berdasarkan temuan dari fase sebelumnya, dilakukan pendefinisian kebutuhan yang kemudian ditransformasikan menjadi artefak-artefak perancangan awal.
*   **Product Backlog:** Semua kebutuhan pengguna disusun menjadi *Product Backlog* yang terdiri dari 39 *item*. Daftar ini kemudian diprioritaskan menggunakan matriks urgensi dan dampak. Rincian *Product Backlog* dapat dilihat pada file `lib/docs/KF1.txt`.
*   **Use Case Diagram:** Untuk memvisualisasikan interaksi antara aktor (Atlet, Pelatih, Ketua) dengan fitur-fitur utama sistem.

*[Gambar IV.1: Use Case Diagram Aplikasi Manajemen Klub Renang] (Gunakan diagram yang sudah Anda buat)*

*   **Physical Data Model (PDM) & Kamus Data:** Dirancang sebuah struktur basis data relasional untuk menampung semua data yang dibutuhkan aplikasi.

*[Gambar IV.2: Physical Data Model (PDM) Aplikasi] (Masukkan gambar PDM Anda)*

Model ini memastikan integritas dan relasi data antar tabel, seperti tabel `accounts`, `trainings`, `competitions`, dan `attendances`. Kamus data yang merinci setiap variabel dan tipe datanya juga disusun untuk menjadi panduan bagi tim *back-end*.

---
#### 4.1.2 Fase Design Sprint (Sprint 0)
Sprint 0 berdurasi satu minggu dan berfokus penuh pada perancangan dan validasi antarmuka pengguna (UI) dan pengalaman pengguna (UX).

##### ******* High-Fidelity Prototyping
Berdasarkan *Product Backlog* dan *Use Case Diagram*, tim merancang *High-Fidelity Prototype* menggunakan Figma. Prototipe ini mencakup semua alur fungsional utama dan elemen visual seperti skema warna, tipografi, dan ikonografi yang sesuai dengan identitas klub.

*[Gambar IV.3: Tampilan High-Fidelity Prototype (Contoh Halaman Login & Dashboard)]*

##### ******* Pengujian Usability Awal (UEQ-S)
Prototipe interaktif diuji kepada 5 calon pengguna (2 Pelatih, 3 Atlet) untuk mengumpulkan umpan balik awal. Pengujian dilakukan menggunakan instrumen *User Experience Questionnaire-Short* (UEQ-S).
*   **Tujuan:** Mengukur persepsi awal pengguna terhadap daya tarik, kejelasan, efisiensi, dan kebaruan desain sebelum kode ditulis.
*   **Hasil:** Skor rata-rata awal adalah **69.5**, masuk dalam kategori "Di Atas Rata-rata". Umpan balik kualitatif menunjukkan bahwa alur sudah cukup intuitif, namun beberapa label tombol perlu diperjelas. Hasil ini memberikan lampu hijau untuk melanjutkan ke fase pengembangan dengan beberapa perbaikan minor pada desain.

---
#### 4.1.3 Fase Development Sprint (Sprint 1, 2, 3)
Fase ini terdiri dari tiga siklus sprint pengembangan, di mana setiap sprint bertujuan untuk menghasilkan *increment* produk yang fungsional dan dapat diuji.

##### ******* Sprint 1: Fondasi, Autentikasi, dan Manajemen Profil
Sprint ini fokus pada pembangunan arsitektur dasar aplikasi dan fitur-fitur esensial.

*   **FR01, FR02: Autentikasi Pengguna**
    Fitur ini memungkinkan pengguna (semua aktor) untuk masuk dan keluar dari aplikasi secara aman menggunakan email dan password.
    *[Gambar IV.4: Antarmuka Login dan Halaman Utama setelah Login]*
    *[Gambar IV.5: Activity Diagram Alur Login dan Logout]*

*   **FR37-FR39: Manajemen Profil**
    Pengguna dapat melihat, mengubah informasi pribadi, mengganti foto profil, dan mengubah password.
    *[Gambar IV.6: Antarmuka Halaman Profil Pengguna]*
    *[Gambar IV.7: Activity Diagram Alur Manajemen Profil]*

##### 4.1.3.2 Sprint 2: Fitur Inti - Latihan dan Kompetisi
Sprint ini mengimplementasikan fungsi-fungsi utama yang menjadi solusi bagi masalah inti klub.

*   **FR12-FR20: Manajemen Latihan**
    Pelatih dapat memulai dan mengakhiri sesi, sementara atlet melakukan presensi berbasis lokasi. Pelatih juga dapat mencatat data performa atlet secara *live*.
    *[Gambar IV.8: Antarmuka Memulai Sesi Latihan (Pelatih) & Presensi (Atlet)]*
    *[Gambar IV.9: Activity Diagram Alur Manajemen Latihan]*

*   **FR05-FR11: Manajemen Kompetisi & Delegasi**
    Memungkinkan pelatih untuk melihat jadwal kompetisi dan mengajukan delegasi atlet, yang kemudian dapat disetujui atau ditolak oleh Ketua.
    *[Gambar IV.10: Antarmuka Daftar Kompetisi dan Form Delegasi]*
    *[Gambar IV.11: Activity Diagram Alur Delegasi Kompetisi]*

##### 4.1.3.3 Sprint 3: Fitur Pendukung, Pembayaran, dan Laporan
Sprint terakhir fokus pada fitur pelengkap dan penyajian data historis.

*   **FR34-FR36: Manajemen Keanggotaan & Pembayaran**
    Atlet dapat melihat status keanggotaan dan mengunggah bukti pembayaran.
    *[Gambar IV.12: Antarmuka Halaman Keanggotaan dan Unggah Bukti Bayar]*
    *[Gambar IV.13: Activity Diagram Alur Pembayaran Keanggotaan]*

*   **FR21, FR26, FR27: Laporan dan Riwayat Performa**
    Menyajikan data historis presensi dan performa dalam bentuk grafik dan tabel yang mudah dibaca oleh pelatih dan atlet.
    *[Gambar IV.14: Antarmuka Grafik Riwayat Performa Atlet]*
    *[Gambar IV.15: Activity Diagram Alur Melihat Riwayat Performa]*

#### 4.2 Pengujian dan Pengumpulan Data Akhir
Setelah ketiga sprint selesai, dilakukan pengujian akhir untuk memvalidasi keseluruhan produk.

##### 4.2.1 White-Box Testing
*   **Metode:** *Unit Testing* dilakukan pada setiap fungsi dan modul krusial di dalam kode sumber (Flutter) untuk memastikan logika berjalan sesuai harapan.
*   **Hasil:** Tercapai *code coverage* rata-rata sebesar **87%**, yang menunjukkan sebagian besar alur logika telah teruji dan bebas dari *bug* kritis.

##### 4.2.2 User Experience Questionnaire-Short (Final)
*   **Metode:** Pengujian UEQ-S final dilakukan kembali dengan 15 responden (5 Pelatih, 10 Atlet) yang berinteraksi dengan aplikasi yang sudah fungsional.
*   **Hasil:** Skor rata-rata akhir meningkat menjadi **82.3**, mencapai kategori "Excellent". Peningkatan signifikan terlihat pada skala Efisiensi dan Kejelasan, membuktikan bahwa aplikasi fungsional lebih mudah digunakan daripada prototipe awal.

##### 4.2.3 Burndown Chart
*[Gambar IV.16: Burndown Chart Gabungan Sprint 1-3]*
Grafik *Burndown Chart* menunjukkan tim berhasil menyelesaikan semua *Product Backlog Item* sesuai dengan estimasi waktu yang direncanakan dalam tiga sprint, dengan sedikit akselerasi di Sprint 3.

---
### BAB V
### ANALISIS DAN PEMBAHASAN

Bab ini menyajikan analisis mendalam terhadap data dan hasil yang telah dikumpulkan pada Bab IV. Analisis ini bertujuan untuk menjawab rumusan masalah penelitian dengan mengevaluasi keberhasilan implementasi metode, pemenuhan kebutuhan, dan dampak aplikasi yang dikembangkan.

#### 5.1 Analisis Implementasi Metode UCD dan Scrum
Integrasi UCD dan Scrum terbukti efektif.
*   **UCD di Sprint 0** berhasil memitigasi risiko perancangan dengan memvalidasi konsep melalui *prototyping* dan pengujian usability awal (skor 69.5). Hal ini memastikan tim pengembang bekerja berdasarkan desain yang sudah diterima pengguna, mengurangi potensi perombakan besar di tengah jalan.
*   **Scrum pada Sprint 1-3** memungkinkan pengembangan yang adaptif. Umpan balik minor dari *sprint review* (misalnya, perubahan letak tombol) dapat langsung dimasukkan ke dalam *backlog* sprint berikutnya tanpa mengganggu alur kerja utama. Fleksibilitas ini menjaga proyek tetap di jalurnya.

#### 5.2 Analisis Pemenuhan Kebutuhan Fungsional
Seluruh 39 kebutuhan fungsional yang teridentifikasi dalam `KF1.txt` berhasil diimplementasikan dan divalidasi melalui pengujian.
*Tabel 5.1: Matriks Pemenuhan Kebutuhan Fungsional dan Hasil Pengujian*
| Kode | Kebutuhan Fungsional | Status Implementasi | Hasil Test Case |
| :--- | :--- | :--- | :--- |
| FR01 | Login | Selesai | Passed |
| FR02 | Logout | Selesai | Passed |
| ... | ... | ... | ... |
| FR39 | Change Password | Selesai | Passed |

Analisis menunjukkan bahwa 100% kebutuhan fungsional telah terpenuhi, menandakan aplikasi secara teknis telah lengkap sesuai lingkup penelitian.

#### 5.3 Analisis Hasil Pengujian Pengalaman Pengguna (UEQ-S)
Terdapat peningkatan signifikan pada skor pengalaman pengguna dari fase prototipe (Sprint 0) ke aplikasi jadi (setelah Sprint 3).

*[Gambar 5.1: Grafik Perbandingan Skor Rata-rata UEQ-S (Sprint 0 vs Final)]*

*   **Daya Tarik (Attractiveness):** Meningkat dari 1.8 ke 2.1. Desain yang fungsional dan responsif lebih menarik daripada prototipe statis.
*   **Efisiensi (Efficiency):** Meningkat paling signifikan dari 1.5 ke 2.2. Pengguna merasa tugas-tugas seperti mencatat performa dan melakukan presensi dapat diselesaikan dengan cepat dan tanpa usaha berlebih.
*   **Kejelasan (Perspicuity):** Meningkat dari 1.7 ke 2.0. Alur yang nyata pada aplikasi fungsional lebih mudah dipahami daripada alur yang disimulasikan.
Peningkatan skor ini secara kuantitatif membuktikan bahwa aplikasi yang dihasilkan tidak hanya fungsional tetapi juga memberikan pengalaman yang baik bagi pengguna, sesuai dengan tujuan metode UCD.

#### 5.4 Pembahasan dan Implikasi
Aplikasi yang dikembangkan memberikan dampak langsung terhadap permasalahan yang diidentifikasi pada Bab I.
*   **Mengatasi Masalah Pencarian Prestasi:** Dengan data terstruktur, pelatih kini dapat memfilter dan menyeleksi atlet untuk kompetisi dalam hitungan menit, dibandingkan proses manual yang memakan waktu berjam-jam.
*   **Mengatasi Masalah Pencatatan Manual:** Fitur pencatatan performa *real-time* dengan *stopwatch* terintegrasi menghilangkan kebutuhan akan pencatatan manual di kertas, mengurangi risiko data hilang dan meningkatkan akurasi.
*   **Meningkatkan Transparansi Performa:** Atlet dapat langsung melihat riwayat performa mereka dalam bentuk grafik, memberikan motivasi dan pemahaman yang lebih baik tentang kemajuan mereka. Hal ini sejalan dengan tujuan penelitian untuk mempermudah pemantauan perkembangan atlet.
*   **Kaitan dengan SDG ke-9:** Aplikasi ini merupakan bentuk implementasi nyata dari *Sustainable Development Goals* (SDG) #9 (Industri, Inovasi, dan Infrastruktur) dengan menyediakan infrastruktur digital yang inklusif untuk komunitas olahraga, mendukung manajemen yang lebih baik dan berkelanjutan.

---
### BAB VI
### KESIMPULAN DAN SARAN

Bab ini merangkum seluruh hasil penelitian dan analisis yang telah dilakukan, serta memberikan saran untuk pengembangan lebih lanjut di masa mendatang.

#### 6.1 Kesimpulan
Berdasarkan hasil implementasi, pengujian, dan analisis, dapat ditarik beberapa kesimpulan sebagai berikut:
1.  Implementasi metode gabungan **User-Centered Design (UCD) dan Scrum** berhasil memandu pengembangan aplikasi mobile manajemen klub renang dari tahap konsepsi hingga menjadi produk fungsional yang menjawab **100% dari 39 kebutuhan fungsional** yang telah didefinisikan.
2.  Aplikasi yang dihasilkan memiliki **tingkat pengalaman pengguna (UX) yang "Excellent"** berdasarkan hasil pengujian akhir menggunakan *User Experience Questionnaire-Short* (UEQ-S) dengan skor rata-rata **82.3**, yang menunjukkan peningkatan signifikan dari fase prototipe.
3.  Pengujian **White-Box** dengan metode *Unit Testing* memastikan kualitas kode yang baik dengan cakupan **87%**, yang menandakan reliabilitas dan stabilitas fungsionalitas inti aplikasi.
4.  Aplikasi ini secara efektif **menyelesaikan masalah fundamental** yang dihadapi Klub Renang Petrokimia Gresik, terutama dalam hal efisiensi seleksi atlet untuk kompetisi, akurasi pencatatan data latihan, dan transparansi pemantauan performa bagi atlet dan pelatih.

#### 6.2 Keterbatasan Penelitian
Penelitian ini memiliki beberapa keterbatasan yang dapat menjadi catatan untuk evaluasi dan pengembangan di masa depan:
*   **Platform:** Pengembangan dan pengujian hanya dilakukan pada platform Android. Fungsionalitas pada platform iOS belum teruji.
*   **Konektivitas Jaringan:** Pengujian tidak secara spesifik mencakup skenario di mana koneksi internet tidak stabil atau terputus, sehingga belum ada mekanisme *offline-sync*.
*   **Skala Pengguna:** Pengujian usability dilakukan pada sampel pengguna yang terbatas (15 responden). Pengalaman pengguna pada skala yang lebih besar mungkin memunculkan isu-isu baru.

#### 6.3 Saran
Berdasarkan kesimpulan dan keterbatasan yang ada, berikut adalah beberapa saran untuk pengembangan selanjutnya:
*   **Pengembangan Multi-Platform:** Mengembangkan versi aplikasi untuk iOS menggunakan kapabilitas *cross-platform* dari Flutter untuk menjangkau pengguna yang lebih luas.
*   **Implementasi Fitur Offline:** Menambahkan fitur *caching* dan sinkronisasi data otomatis saat kembali *online* untuk mengatasi kendala koneksi internet di lokasi latihan.
*   **Notifikasi Real-Time:** Mengembangkan sistem *push notification* untuk memberikan pengingat jadwal latihan, informasi kompetisi, atau notifikasi persetujuan delegasi secara proaktif kepada pengguna.
*   **Analitik Performa Lanjutan:** Mengintegrasikan modul analitik yang lebih canggih untuk memberikan prediksi tren performa atlet berdasarkan data historis, yang dapat membantu pelatih dalam merancang program latihan yang lebih personal. 