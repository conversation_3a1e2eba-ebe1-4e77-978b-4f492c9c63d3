# BAB V: ANALISIS DAN PEMBAHASAN

Bab ini menyajikan analisis mendalam terhadap data dan hasil yang telah dikumpulkan pada Bab IV.

## 5.1 Analisis Implementasi Metode UCD dan Scrum
Integrasi UCD (di Sprint 0) dan <PERSON> (Sprint 1-3) terbukti efektif memitigasi risiko perancangan dan memungkinkan pengembangan yang adaptif.

## 5.2 Ana<PERSON><PERSON>an Fungsional
100% dari 39 kebutuhan fungsional berhasil diimplementasikan dan divalidasi.

| Kode | Kebutuhan Fungsional | Status Implementasi | Hasil Test Case |
| :--- | :--- | :--- | :--- |
| FR01 | Login | Selesai | Passed |
| FR02 | Logout | Selesai | Passed |
| ... | ... | ... | ... |
| FR39 | Change Password | Selesai | Passed |

## 5.3 Analisis <PERSON>gguna (UEQ-S)
Peningkatan signifikan skor UX dari fase prototipe (69.5) ke aplikasi jadi (82.3).

![UEQ Comparison](placeholder_ueq_chart.png "Gambar 5.1: Grafik Perbandingan Skor Rata-rata UEQ-S")

-   **Daya Tarik:** 1.8 ➔ 2.1
-   **Efisiensi:** 1.5 ➔ 2.2
-   **Kejelasan:** 1.7 ➔ 2.0

## 5.4 Pembahasan dan Implikasi
Aplikasi secara efektif memberikan solusi untuk:
-   Mengatasi masalah pencarian prestasi.
-   Mengatasi masalah pencatatan manual.
-   Meningkatkan transparansi performa.
-   Berkontribusi pada SDG ke-9 melalui penyediaan infrastruktur digital. 