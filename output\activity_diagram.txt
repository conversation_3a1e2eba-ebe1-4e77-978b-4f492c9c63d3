'Aplikasi SiMenang KRPG' {
    |Pengguna, Sistem| {
        $Pengguna$
        (Pengguna) Buka Aplikasi;
        (Sistem) Tampilk<PERSON>;
        (Pengguna) Masukkan kredensial;
        (Sistem) Validasi kredensial;
        <Sistem> Apakah kredensial valid? {
            -Ya- {
                (Sistem) Arahkan ke Halaman Utama;
                (Pengguna) Lihat Halaman Utama;
                <Pengguna> Pilih menu navigasi {
                    -Profil- {
                        (Sistem) <PERSON><PERSON>lkan Halaman Profil;
                        (Pengguna) Lihat & edit profil;
                    }
                    -Atlet- {
                        (Sistem) Tampilkan Daftar Atlet;
                        (Pengguna) Pilih satu atlet;
                        (Sistem) Tampilkan Detail Atlet;
                    }
                    -Kelas- {
                        (Sistem) Tampilkan Daftar Kelas;
                        (Pengguna) Pilih satu kelas;
                        (Sistem) Tampilkan Detail Kelas;
                    }
                    -Kompetisi- {
                        (Sistem) Tampilkan Daftar Kompetisi;
                        (Pengguna) Pilih satu kompetisi;
                        (Sistem) Tampilkan <PERSON>;
                    }
                    -Latihan- {
                        (Sistem) <PERSON><PERSON><PERSON><PERSON>;
                        (Pengguna) <PERSON><PERSON><PERSON> jadwal latihan;
                        (Sistem) Tampilkan Detail Latihan;
                        <Pengguna> Mulai Latihan? {
                            -Ya- {
                                (Sistem) Tampilkan Layar Sesi Latihan;
                                (Pengguna) Melakukan sesi latihan;
                                (Sistem) Lacak data GPS & waktu;
                                <Pengguna> Selesaikan Latihan? {
                                    -Ya- {
                                        (Sistem) Tampilkan Dialog Perekaman Statistik;
                                        (Pengguna) Isi statistik;
                                        (Sistem) Simpan sesi latihan;
                                        (Sistem) Kembali ke Detail Latihan;
                                    }
                                    -Tidak- {
                                        (Pengguna) Lanjutkan sesi;
                                    }
                                }
                                >Sistem<;
                            }
                            -Tidak- {
                                <Pengguna> Ambil Absen? {
                                    -Ya- {
                                        (Sistem) Tampilkan Halaman Absensi;
                                        (Pengguna) Mengambil absen (misal: scan QR);
                                        (Sistem) Validasi & simpan absen;
                                    }
                                    -Tidak- {
                                        // Kembali
                                    }
                                }
                                >Sistem<;
                            }
                        }
                        >Sistem<;
                    }
                }
                >Sistem<;
            }
            -Tidak- {
                (Sistem) Tampilkan pesan error;
                (Pengguna) Coba lagi;
            }
        }
        >Sistem<;
        @Pengguna@
    }
} 