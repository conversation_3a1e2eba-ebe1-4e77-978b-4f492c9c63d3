import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controllers/auth_controller.dart';
import '../models/user_model.dart';

/// Utility class for generating role-based titles throughout the app
class RoleTitleHelper {
  /// Creates a role-based title widget that automatically updates based on the current user's role
  /// 
  /// Example usage:
  /// ```dart
  /// appBar: AppBar(
  ///   title: RoleTitleHelper.createRoleBasedTitle(context, 'Home'),
  /// ),
  /// ```
  static Widget createRoleBasedTitle(BuildContext context, String baseTitle) {
    return Consumer<AuthController>(
      builder: (context, authController, child) {
        final userRole = authController.currentUser?.role.displayName ?? '';
        final roleBasedTitle = userRole.isNotEmpty ? '$userRole $baseTitle' : baseTitle;
        return Text(roleBasedTitle);
      },
    );
  }

  /// Gets a role-based title string without creating a widget
  /// 
  /// Example usage:
  /// ```dart
  /// String title = RoleTitleHelper.getRoleBasedTitle(context, 'Dashboard');
  /// ```
  static String getRoleBasedTitle(BuildContext context, String baseTitle) {
    final authController = context.read<AuthController>();
    final userRole = authController.currentUser?.role.displayName ?? '';
    return userRole.isNotEmpty ? '$userRole $baseTitle' : baseTitle;
  }

  /// Gets the current user's role display name
  static String getCurrentUserRole(BuildContext context) {
    final authController = context.read<AuthController>();
    return authController.currentUser?.role.displayName ?? '';
  }

  /// Checks if the current user has a specific role
  static bool hasRole(BuildContext context, UserRole role) {
    final authController = context.read<AuthController>();
    return authController.currentUser?.role == role;
  }

  /// Gets role-specific colors for UI elements
  static Color getRoleColor(UserRole role) {
    switch (role) {
      case UserRole.leader:
        return Colors.purple;
      case UserRole.coach:
        return Colors.blue;
      case UserRole.athlete:
        return Colors.green;
      case UserRole.guest:
        return Colors.grey;
    }
  }

  /// Gets role-specific icons
  static IconData getRoleIcon(UserRole role) {
    switch (role) {
      case UserRole.leader:
        return Icons.admin_panel_settings;
      case UserRole.coach:
        return Icons.sports;
      case UserRole.athlete:
        return Icons.fitness_center;
      case UserRole.guest:
        return Icons.person_outline;
    }
  }
}
