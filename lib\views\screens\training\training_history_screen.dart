import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../controllers/training_controller.dart';
import '../../../components/cards/krpg_card.dart';
import '../../../components/ui/krpg_badge.dart';
import '../../../components/forms/krpg_search_bar.dart';
import '../../../design_system/krpg_theme.dart';
import '../../../design_system/krpg_text_styles.dart';

class TrainingHistoryScreen extends StatefulWidget {
  const TrainingHistoryScreen({super.key});

  @override
  State<TrainingHistoryScreen> createState() => _TrainingHistoryScreenState();
}

class _TrainingHistoryScreenState extends State<TrainingHistoryScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<Map<String, dynamic>> _trainingHistory = [];
  List<Map<String, dynamic>> _filteredHistory = [];
  bool _isLoading = true;
  String _selectedFilter = 'all';

  @override
  void initState() {
    super.initState();
    _loadTrainingHistory();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadTrainingHistory() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final controller = context.read<TrainingController>();
      final history = await controller.getTrainingSessionHistory();
      
      setState(() {
        _trainingHistory = history ?? _generateDummyTrainingHistory();
        _filteredHistory = List.from(_trainingHistory);
      });
    } catch (e) {
      // Fallback to dummy data for UEQ-S testing
      setState(() {
        _trainingHistory = _generateDummyTrainingHistory();
        _filteredHistory = List.from(_trainingHistory);
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<Map<String, dynamic>> _generateDummyTrainingHistory() {
    return [
      {
        'id': 'session_001',
        'training_name': 'Basic Karate Techniques',
        'date': '2025-01-30',
        'start_time': '19:00',
        'end_time': '21:00',
        'duration': '2 hours',
        'participants_count': 15,
        'coach': 'Sensei Budi Santoso',
        'location': 'Dojo Utama - Lantai 1',
        'status': 'completed',
        'training_type': 'regular',
        'focus_area': 'Kata & Kihon',
        'performance_summary': {
          'avg_performance': 8.2,
          'best_performer': 'Ahmad Ridwan',
          'improvement_areas': ['Kumite defensive techniques', 'Stamina building']
        }
      },
      {
        'id': 'session_002',
        'training_name': 'Kumite Competition Prep',
        'date': '2025-01-28',
        'start_time': '17:30',
        'end_time': '19:30',
        'duration': '2 hours',
        'participants_count': 12,
        'coach': 'Sensei Ana Putri',
        'location': 'Dojo Utama - Lantai 2',
        'status': 'completed',
        'training_type': 'competition_prep',
        'focus_area': 'Kumite Sparring',
        'performance_summary': {
          'avg_performance': 7.8,
          'best_performer': 'Sari Dewi',
          'improvement_areas': ['Counter-attack timing', 'Distance management']
        }
      },
      {
        'id': 'session_003',
        'training_name': 'Kata Championship Training',
        'date': '2025-01-26',
        'start_time': '20:00',
        'end_time': '21:30',
        'duration': '1.5 hours',
        'participants_count': 8,
        'coach': 'Sensei Citra Wulan',
        'location': 'Dojo Utama - Lantai 1',
        'status': 'completed',
        'training_type': 'specialized',
        'focus_area': 'Advanced Kata',
        'performance_summary': {
          'avg_performance': 8.7,
          'best_performer': 'Budi Prakoso',
          'improvement_areas': ['Breathing rhythm', 'Eye contact and zanshin']
        }
      },
      {
        'id': 'session_004',
        'training_name': 'Physical Conditioning',
        'date': '2025-01-24',
        'start_time': '18:00',
        'end_time': '19:00',
        'duration': '1 hour',
        'participants_count': 20,
        'coach': 'Pelatih Fitnes Rio',
        'location': 'Gym Area',
        'status': 'completed',
        'training_type': 'conditioning',
        'focus_area': 'Strength & Endurance',
        'performance_summary': {
          'avg_performance': 7.5,
          'best_performer': 'Lisa Handayani',
          'improvement_areas': ['Core strength', 'Flexibility']
        }
      },
      {
        'id': 'session_005',
        'training_name': 'Junior Class - Basics',
        'date': '2025-01-22',
        'start_time': '16:00',
        'end_time': '17:00',
        'duration': '1 hour',
        'participants_count': 10,
        'coach': 'Sensei Mira Sari',
        'location': 'Dojo Junior',
        'status': 'completed',
        'training_type': 'junior',
        'focus_area': 'Basic Movements',
        'performance_summary': {
          'avg_performance': 8.0,
          'best_performer': 'Kevin Pratama',
          'improvement_areas': ['Balance', 'Coordination']
        }
      },
    ];
  }

  void _performSearch() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredHistory = _trainingHistory.where((session) {
        final matchesQuery = query.isEmpty ||
            session['training_name'].toString().toLowerCase().contains(query) ||
            session['coach'].toString().toLowerCase().contains(query) ||
            session['focus_area'].toString().toLowerCase().contains(query);
        
        final matchesFilter = _selectedFilter == 'all' ||
            session['training_type'] == _selectedFilter ||
            session['status'] == _selectedFilter;
        
        return matchesQuery && matchesFilter;
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Training Session History'),
        backgroundColor: KRPGTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Column(
        children: [
          // Search and Filter Section
          Container(
            padding: const EdgeInsets.all(KRPGTheme.spacingMd),
            child: Column(
              children: [
                KRPGSearchBar(
                  hintText: 'Search training sessions...',
                  onChanged: (value) {
                    _searchController.text = value;
                    _performSearch();
                  },
                ),
                const SizedBox(height: KRPGTheme.spacingSm),
                _buildFilterSection(),
              ],
            ),
          ),
          
          // Content
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : RefreshIndicator(
                    onRefresh: _loadTrainingHistory,
                    child: _filteredHistory.isEmpty
                        ? _buildEmptyState()
                        : ListView.builder(
                            padding: const EdgeInsets.symmetric(horizontal: KRPGTheme.spacingMd),
                            itemCount: _filteredHistory.length,
                            itemBuilder: (context, index) {
                              final session = _filteredHistory[index];
                              return _buildTrainingSessionCard(session);
                            },
                          ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          _buildFilterChip('All', 'all'),
          const SizedBox(width: KRPGTheme.spacingSm),
          _buildFilterChip('Regular', 'regular'),
          const SizedBox(width: KRPGTheme.spacingSm),
          _buildFilterChip('Competition Prep', 'competition_prep'),
          const SizedBox(width: KRPGTheme.spacingSm),
          _buildFilterChip('Specialized', 'specialized'),
          const SizedBox(width: KRPGTheme.spacingSm),
          _buildFilterChip('Conditioning', 'conditioning'),
          const SizedBox(width: KRPGTheme.spacingSm),
          _buildFilterChip('Junior', 'junior'),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String value) {
    final isSelected = _selectedFilter == value;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedFilter = value;
        });
        _performSearch();
      },
      backgroundColor: isSelected ? KRPGTheme.primaryColor.withOpacity(0.1) : null,
      selectedColor: KRPGTheme.primaryColor.withOpacity(0.2),
      checkmarkColor: KRPGTheme.primaryColor,
    );
  }

  Widget _buildTrainingSessionCard(Map<String, dynamic> session) {
    return Padding(
      padding: const EdgeInsets.only(bottom: KRPGTheme.spacingMd),
      child: KRPGCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Text(
                    session['training_name'],
                    style: KRPGTextStyles.heading6,
                  ),
                ),
                KRPGBadge(
                  text: session['training_type'].toString().replaceAll('_', ' ').toUpperCase(),
                  backgroundColor: _getTrainingTypeColor(session['training_type']).withOpacity(0.1),
                  textColor: _getTrainingTypeColor(session['training_type']),
                ),
              ],
            ),
            const SizedBox(height: KRPGTheme.spacingSm),
            
            // Basic Info
            Row(
              children: [
                Icon(Icons.calendar_today, size: 16, color: KRPGTheme.textMedium),
                const SizedBox(width: KRPGTheme.spacingXs),
                Text(
                  session['date'],
                  style: KRPGTextStyles.bodySmall.copyWith(color: KRPGTheme.textMedium),
                ),
                const SizedBox(width: KRPGTheme.spacingMd),
                Icon(Icons.access_time, size: 16, color: KRPGTheme.textMedium),
                const SizedBox(width: KRPGTheme.spacingXs),
                Text(
                  '${session['start_time']} - ${session['end_time']}',
                  style: KRPGTextStyles.bodySmall.copyWith(color: KRPGTheme.textMedium),
                ),
              ],
            ),
            const SizedBox(height: KRPGTheme.spacingXs),
            
            Row(
              children: [
                Icon(Icons.person, size: 16, color: KRPGTheme.textMedium),
                const SizedBox(width: KRPGTheme.spacingXs),
                Text(
                  session['coach'],
                  style: KRPGTextStyles.bodySmall.copyWith(color: KRPGTheme.textMedium),
                ),
                const SizedBox(width: KRPGTheme.spacingMd),
                Icon(Icons.location_on, size: 16, color: KRPGTheme.textMedium),
                const SizedBox(width: KRPGTheme.spacingXs),
                Expanded(
                  child: Text(
                    session['location'],
                    style: KRPGTextStyles.bodySmall.copyWith(color: KRPGTheme.textMedium),
                  ),
                ),
              ],
            ),
            const SizedBox(height: KRPGTheme.spacingSm),
            
            // Performance Summary
            if (session['performance_summary'] != null) ...[
              Container(
                padding: const EdgeInsets.all(KRPGTheme.spacingSm),
                decoration: BoxDecoration(
                  color: KRPGTheme.backgroundAccent,
                  borderRadius: BorderRadius.circular(KRPGTheme.radiusSm),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          'Performance Summary',
                          style: KRPGTextStyles.bodySmall.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const Spacer(),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: KRPGTheme.spacingXs,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: KRPGTheme.primaryColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(KRPGTheme.radiusXs),
                          ),
                          child: Text(
                            '${session['participants_count']} participants',
                            style: KRPGTextStyles.bodySmall.copyWith(
                              color: KRPGTheme.primaryColor,
                              fontSize: 10,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: KRPGTheme.spacingXs),
                    Row(
                      children: [
                        Text(
                          'Avg Score: ${session['performance_summary']['avg_performance']}',
                          style: KRPGTextStyles.bodySmall,
                        ),
                        const SizedBox(width: KRPGTheme.spacingMd),
                        Text(
                          'Best: ${session['performance_summary']['best_performer']}',
                          style: KRPGTextStyles.bodySmall.copyWith(
                            color: KRPGTheme.primaryColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    if (session['performance_summary']['improvement_areas'] != null) ...[
                      const SizedBox(height: KRPGTheme.spacingXs),
                      Text(
                        'Focus Areas: ${session['performance_summary']['improvement_areas'].join(', ')}',
                        style: KRPGTextStyles.bodySmall.copyWith(
                          color: KRPGTheme.textMedium,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.history,
            size: 64,
            color: KRPGTheme.neutralMedium,
          ),
          const SizedBox(height: KRPGTheme.spacingMd),
          Text(
            'No Training History Found',
            style: KRPGTextStyles.heading5,
          ),
          const SizedBox(height: KRPGTheme.spacingSm),
          Text(
            'Training sessions you participate in will appear here',
            style: KRPGTextStyles.bodyMedium.copyWith(
              color: KRPGTheme.textMedium,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Color _getTrainingTypeColor(String type) {
    switch (type) {
      case 'competition_prep':
        return KRPGTheme.warningColor;
      case 'specialized':
        return KRPGTheme.primaryColor;
      case 'conditioning':
        return KRPGTheme.successColor;
      case 'junior':
        return KRPGTheme.infoColor;
      case 'regular':
      default:
        return KRPGTheme.neutralMedium;
    }
  }
}
