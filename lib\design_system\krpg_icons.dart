import 'package:flutter/material.dart';

/// KRPG Design System - Icons
/// 
/// This class defines consistent icons to be used throughout the app.
class KRPGIcons {
  // Private constructor to prevent instantiation
  KRPGIcons._();
  
  // Navigation
  static const IconData home = Icons.home_rounded;
  static const IconData calendar = Icons.calendar_today_rounded;
  static const IconData training = Icons.fitness_center_rounded;
  static const IconData competition = Icons.emoji_events_rounded;
  static const IconData profile = Icons.person_rounded;
  static const IconData settings = Icons.settings_rounded;
  static const IconData notifications = Icons.notifications_rounded;
  static const IconData menu = Icons.menu_rounded;
  static const IconData back = Icons.arrow_back_ios_rounded;
  static const IconData forward = Icons.arrow_forward_ios_rounded;
  
  // Actions
  static const IconData add = Icons.add_rounded;
  static const IconData edit = Icons.edit_rounded;
  static const IconData delete = Icons.delete_rounded;
  static const IconData save = Icons.save_rounded;
  static const IconData share = Icons.share_rounded;
  static const IconData download = Icons.download_rounded;
  static const IconData upload = Icons.upload_rounded;
  static const IconData search = Icons.search_rounded;
  static const IconData filter = Icons.filter_list_rounded;
  static const IconData sort = Icons.sort_rounded;
  static const IconData refresh = Icons.refresh_rounded;
  static const IconData more = Icons.more_vert_rounded;
  
  // Status
  static const IconData success = Icons.check_circle_rounded;
  static const IconData warning = Icons.warning_rounded;
  static const IconData error = Icons.error_rounded;
  static const IconData info = Icons.info_rounded;
  static const IconData help = Icons.help_rounded;
  static const IconData lock = Icons.lock_rounded;
  static const IconData unlock = Icons.lock_open_rounded;
  
  // Communication
  static const IconData email = Icons.email_rounded;
  static const IconData phone = Icons.phone_rounded;
  static const IconData chat = Icons.chat_rounded;
  static const IconData comment = Icons.comment_rounded;
  static const IconData send = Icons.send_rounded;
  
  // Media
  static const IconData image = Icons.image_rounded;
  static const IconData camera = Icons.camera_alt_rounded;
  static const IconData video = Icons.videocam_rounded;
  static const IconData attachment = Icons.attach_file_rounded;
  static const IconData mic = Icons.mic_rounded;
  static const IconData play = Icons.play_arrow_rounded;
  static const IconData pause = Icons.pause_rounded;
  static const IconData stop = Icons.stop_rounded;
  
  // Swimming specific
  static const IconData swimming = Icons.pool_rounded;
  static const IconData coach = Icons.sports_rounded;
  static const IconData athlete = Icons.directions_run_rounded;
  static const IconData medal = Icons.military_tech_rounded;
  static const IconData trophy = Icons.emoji_events_rounded;
  static const IconData stopwatch = Icons.timer_rounded;
  static const IconData whistle = Icons.sports_rounded;
  static const IconData swimLane = Icons.view_week_rounded;
  
  // UI elements
  static const IconData close = Icons.close_rounded;
  static const IconData check = Icons.check_rounded;
  static const IconData expand = Icons.expand_more_rounded;
  static const IconData collapse = Icons.expand_less_rounded;
  static const IconData fullscreen = Icons.fullscreen_rounded;
  static const IconData exitFullscreen = Icons.fullscreen_exit_rounded;
  static const IconData zoomIn = Icons.zoom_in_rounded;
  static const IconData zoomOut = Icons.zoom_out_rounded;
  static const IconData visible = Icons.visibility_rounded;
  static const IconData invisible = Icons.visibility_off_rounded;
  
  // Misc
  static const IconData location = Icons.location_on_rounded;
  static const IconData time = Icons.access_time_rounded;
  static const IconData date = Icons.date_range_rounded;
  static const IconData person = Icons.person_rounded;
  static const IconData group = Icons.group_rounded;
  static const IconData star = Icons.star_rounded;
  static const IconData favorite = Icons.favorite_rounded;
  static const IconData bookmark = Icons.bookmark_rounded;
  static const IconData link = Icons.link_rounded;
  static const IconData dashboard = Icons.dashboard_rounded;
  static const IconData chart = Icons.bar_chart_rounded;
  static const IconData list = Icons.list_rounded;
  static const IconData grid = Icons.grid_view_rounded;
  static const IconData invoice = Icons.receipt_long_rounded;
} 